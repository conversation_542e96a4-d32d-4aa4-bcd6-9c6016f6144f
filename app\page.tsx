'use client'

import { useState, useRef } from 'react'
import QRCode from 'qrcode'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Textarea } from '@/components/ui/textarea'
import { Label } from '@/components/ui/label'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { Skeleton } from '@/components/ui/skeleton'
import { Progress } from '@/components/ui/progress'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Download, QrCode, Copy, Check, Settings, Palette, RefreshCw } from 'lucide-react'
import { toast } from 'sonner'

export default function QRCodeGenerator() {
  const [text, setText] = useState('')
  const [qrCodeUrl, setQrCodeUrl] = useState('')
  const [isLoading, setIsLoading] = useState(false)
  const [copied, setCopied] = useState(false)
  const [progress, setProgress] = useState(0)
  const [qrSize, setQrSize] = useState(300)
  const [darkColor, setDarkColor] = useState('#000000')
  const [lightColor, setLightColor] = useState('#FFFFFF')
  const [showCustomization, setShowCustomization] = useState(false)
  const canvasRef = useRef<HTMLCanvasElement>(null)

  const generateQRCode = async () => {
    if (!text.trim()) {
      toast.error('Silakan masukkan teks atau URL terlebih dahulu')
      return
    }

    setIsLoading(true)
    setProgress(0)

    try {
      // Simulate progress
      setProgress(25)

      const qrOptions = {
        width: qrSize,
        margin: 2,
        color: {
          dark: darkColor,
          light: lightColor
        }
      }

      setProgress(50)

      const url = await QRCode.toDataURL(text, qrOptions)
      setQrCodeUrl(url)

      setProgress(75)

      // Generate canvas version for download
      if (canvasRef.current) {
        await QRCode.toCanvas(canvasRef.current, text, qrOptions)
      }

      setProgress(100)
      toast.success('QR Code berhasil dibuat!')

    } catch (error) {
      console.error('Error generating QR code:', error)
      toast.error('Gagal membuat QR Code. Silakan coba lagi.')
    } finally {
      setIsLoading(false)
      setTimeout(() => setProgress(0), 1000)
    }
  }



  const copyToClipboard = async () => {
    if (!qrCodeUrl) return

    try {
      const response = await fetch(qrCodeUrl)
      const blob = await response.blob()
      await navigator.clipboard.write([
        new ClipboardItem({ 'image/png': blob })
      ])
      setCopied(true)
      toast.success('QR Code berhasil disalin ke clipboard!')
      setTimeout(() => setCopied(false), 2000)
    } catch (error) {
      console.error('Error copying to clipboard:', error)
      toast.error('Gagal menyalin QR Code. Silakan coba lagi.')
    }
  }

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault()
      generateQRCode()
    }
  }

  const downloadQRCode = () => {
    if (!qrCodeUrl) return

    const link = document.createElement('a')
    link.download = `qrcode-${Date.now()}.png`
    link.href = qrCodeUrl
    link.click()
    toast.success('QR Code berhasil diunduh!')
  }

  return (
    <TooltipProvider>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-indigo-50 to-purple-50 dark:from-gray-900 dark:via-blue-900 dark:to-indigo-900 py-8 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto">
          {/* Header */}
          <div className="text-center mb-8">
            <div className="flex items-center justify-center mb-6">
              <div className="relative">
                <QrCode className="h-16 w-16 text-indigo-600 dark:text-indigo-400" />
                <div className="absolute -top-1 -right-1">
                  <Badge variant="secondary" className="text-xs">v2.0</Badge>
                </div>
              </div>
            </div>
            <h1 className="text-5xl font-bold bg-gradient-to-r from-indigo-600 to-purple-600 bg-clip-text text-transparent mb-4">
              QR Code Generator
            </h1>
            <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
              Buat QR code untuk teks, URL, atau data apapun dengan mudah.
              Dilengkapi dengan kustomisasi warna dan ukuran.
            </p>
          </div>

          {/* Main Content */}
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            {/* Input Section */}
            <div className="lg:col-span-2">
              <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <CardTitle className="text-2xl">Generate QR Code</CardTitle>
                      <CardDescription className="text-base mt-2">
                        Masukkan teks atau URL yang ingin Anda ubah menjadi QR code
                      </CardDescription>
                    </div>
                    <Tooltip>
                      <TooltipTrigger asChild>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => setShowCustomization(!showCustomization)}
                        >
                          <Settings className="h-4 w-4" />
                        </Button>
                      </TooltipTrigger>
                      <TooltipContent>
                        <p>Pengaturan Kustomisasi</p>
                      </TooltipContent>
                    </Tooltip>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="space-y-3">
                    <Label htmlFor="text-input" className="text-base font-medium">
                      Teks atau URL
                    </Label>
                    <Textarea
                      id="text-input"
                      placeholder="Masukkan teks, URL, atau data apapun di sini..."
                      value={text}
                      onChange={(e) => setText(e.target.value)}
                      onKeyDown={handleKeyDown}
                      className="min-h-[100px] text-base resize-none"
                      rows={4}
                    />
                  </div>

                  {/* Customization Panel */}
                  {showCustomization && (
                    <>
                      <Separator />
                      <div className="space-y-4 p-4 bg-muted/50 rounded-lg">
                        <div className="flex items-center gap-2 mb-3">
                          <Palette className="h-4 w-4" />
                          <span className="font-medium">Kustomisasi QR Code</span>
                        </div>

                        <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="qr-size">Ukuran (px)</Label>
                            <Input
                              id="qr-size"
                              type="number"
                              min="200"
                              max="800"
                              value={qrSize}
                              onChange={(e) => setQrSize(Number(e.target.value))}
                              className="text-center"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="dark-color">Warna Gelap</Label>
                            <Input
                              id="dark-color"
                              type="color"
                              value={darkColor}
                              onChange={(e) => setDarkColor(e.target.value)}
                              className="h-10 p-1"
                            />
                          </div>

                          <div className="space-y-2">
                            <Label htmlFor="light-color">Warna Terang</Label>
                            <Input
                              id="light-color"
                              type="color"
                              value={lightColor}
                              onChange={(e) => setLightColor(e.target.value)}
                              className="h-10 p-1"
                            />
                          </div>
                        </div>
                      </div>
                    </>
                  )}

                  {/* Progress Bar */}
                  {isLoading && (
                    <div className="space-y-2">
                      <div className="flex justify-between text-sm">
                        <span>Membuat QR Code...</span>
                        <span>{progress}%</span>
                      </div>
                      <Progress value={progress} className="h-2" />
                    </div>
                  )}

                  {/* Generate Button */}
                  <Button
                    onClick={generateQRCode}
                    disabled={!text.trim() || isLoading}
                    className="w-full"
                    size="lg"
                  >
                    {isLoading ? (
                      <>
                        <RefreshCw className="mr-2 h-4 w-4 animate-spin" />
                        Generating...
                      </>
                    ) : (
                      <>
                        <QrCode className="mr-2 h-4 w-4" />
                        Generate QR Code
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            </div>

            {/* QR Code Display Section */}
            <div className="lg:col-span-1">
              <Card className="shadow-xl border-0 bg-white/80 dark:bg-gray-800/80 backdrop-blur-sm h-fit">
                <CardHeader>
                  <CardTitle className="text-xl">QR Code Result</CardTitle>
                  <CardDescription>
                    QR code Anda akan muncul di sini
                  </CardDescription>
                </CardHeader>
                <CardContent className="space-y-4">
                  {qrCodeUrl ? (
                    <>
                      <div className="flex justify-center">
                        <div className="bg-white p-6 rounded-xl shadow-lg border-2 border-dashed border-gray-200 dark:border-gray-700">
                          <img
                            src={qrCodeUrl}
                            alt="Generated QR Code"
                            className="max-w-full h-auto rounded-lg"
                            style={{ maxWidth: `${Math.min(qrSize, 300)}px` }}
                          />
                        </div>
                      </div>

                      <Separator />

                      <div className="grid grid-cols-2 gap-3">
                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              onClick={downloadQRCode}
                              variant="outline"
                              className="w-full"
                            >
                              <Download className="mr-2 h-4 w-4" />
                              Download
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Unduh QR Code sebagai PNG</p>
                          </TooltipContent>
                        </Tooltip>

                        <Tooltip>
                          <TooltipTrigger asChild>
                            <Button
                              onClick={copyToClipboard}
                              variant="outline"
                              className="w-full"
                            >
                              {copied ? (
                                <>
                                  <Check className="mr-2 h-4 w-4" />
                                  Copied!
                                </>
                              ) : (
                                <>
                                  <Copy className="mr-2 h-4 w-4" />
                                  Copy
                                </>
                              )}
                            </Button>
                          </TooltipTrigger>
                          <TooltipContent>
                            <p>Salin QR Code ke clipboard</p>
                          </TooltipContent>
                        </Tooltip>
                      </div>

                      {/* QR Code Info */}
                      <div className="space-y-2 text-sm text-muted-foreground">
                        <div className="flex justify-between">
                          <span>Ukuran:</span>
                          <Badge variant="secondary">{qrSize}x{qrSize}px</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Karakter:</span>
                          <Badge variant="secondary">{text.length}</Badge>
                        </div>
                      </div>
                    </>
                  ) : (
                    <div className="flex flex-col items-center justify-center py-12 text-center">
                      <Skeleton className="w-48 h-48 rounded-lg mb-4" />
                      <p className="text-muted-foreground">
                        QR code akan muncul di sini setelah Anda memasukkan teks
                      </p>
                    </div>
                  )}

                  {/* Hidden canvas for download functionality */}
                  <canvas
                    ref={canvasRef}
                    style={{ display: 'none' }}
                  />
                </CardContent>
              </Card>
            </div>
          </div>

          {/* Footer */}
          <div className="mt-12 text-center">
            <Separator className="mb-6" />
            <p className="text-sm text-muted-foreground">
              QR Code Generator dibuat dengan ❤️ menggunakan Next.js 15 & shadcn/ui
            </p>
          </div>
        </div>
      </div>
    </TooltipProvider>
  )
}